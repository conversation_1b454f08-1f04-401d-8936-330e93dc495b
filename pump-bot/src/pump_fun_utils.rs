use std::{str::FromStr, time::Instant};

use solana_client::{
    nonblocking::rpc_client::RpcClient,
    rpc_request::TokenAccountsFilter,
};
use solana_sdk::{
    compute_budget::ComputeBudgetInstruction,
    hash::Hash,
    instruction::{AccountMeta, Instruction},
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_instruction, system_program,
    sysvar::rent,
    transaction::{Transaction, VersionedTransaction},
    message::VersionedMessage,
};
use spl_associated_token_account::{
    get_associated_token_address,
    instruction::create_associated_token_account,
};
use base64::{engine::general_purpose, Engine as _};
use solana_trader_proto::api::TransactionMessage;
use std::env;
use serde_json;
use bincode;
use reqwest::Client;
use base64::decode;
use serde_json::json;

// === Constants ===
pub const PHOTON_PROGRAM_ID: &str = "BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW";
pub const PUMP_FUN_PROGRAM_ID: &str = "6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P";
pub const PUMP_FUN_FEE_RECIPIENT: &str = "62qc2CNXwrYqQScmEdiZFFAnJR262PxWEuNQtxfafNgV";
pub const PHOTON_FEE_VAULT: &str = "AVUCZyuT35YSuj4RH7fwiyPu82Djn2Hfg7y2ND2XcnZH";
pub const EVENT_AUTHORITY: &str = "Ce6TQqeHC9p8KetsN6JsjHK7UTZk7nasjjnr7XxXp9F1";
pub const BLOXROUTE_TIP_WALLET: &str = "HWEoBxYs7ssKuudEjzjmpfJVX7Dvi7wescFsVx2L5yoY";
pub const JITO_TIP_WALLET: &str = "96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5";

// === SOL Amount Constants ===
pub const COMPUTE_UNIT_PRICE: u64 = 6_666_666;         // 0.********** SOL
pub const COMPUTE_UNIT_LIMIT: u32 = 150_000;
pub const DEFAULT_SWAP_AMOUNT: u64 = 1_000_000;        // 0.001 SOL to be swapped
pub const PHOTON_FEE_AMOUNT: u64 = 1_000_000;           // 0.001 SOL fee
pub const BLOXROUTE_TIP_AMOUNT: u64 = 1_000_000;        // 0.001 SOL tip to bloXroute
pub const JITO_TIP_AMOUNT: u64 = 1_000_000;             // 0.001 SOL tip to Jito
pub const SLIPPAGE_BPS: u64 = 10000;                    // 1% slippage (in basis points)

pub fn create_tip_instruction(sender: &Pubkey, recipient: &str, amount: u64) -> Instruction {
    let recipient_pubkey = Pubkey::from_str(recipient).unwrap();
    system_instruction::transfer(sender, &recipient_pubkey, amount)
}

pub async fn check_and_create_ata_if_needed(
    user_keypair: &Keypair,
    mint_address: &Pubkey,
) -> Result<Option<Instruction>, Box<dyn std::error::Error + Send + Sync + 'static>> {
    // Helius endpoint with your API key
    let helius_api_url = "https://mainnet.helius-rpc.com/?api-key=77fb1cea-0cde-4891-b6e0-70b1b34cd438";
    let client = reqwest::Client::new();

    // Build the JSON request payload as specified
    let request_body = serde_json::json!({
        "jsonrpc": "2.0",
        "id": "",
        "method": "getTokenAccounts",
        "params": {
            "mint": mint_address.to_string(),
            "owner": user_keypair.pubkey().to_string()
        }
    });

    // Send the POST request to Helius
    let response = client.post(helius_api_url)
        .json(&request_body)
        .send()
        .await?;

    // Parse the response JSON
    let json: serde_json::Value = response.json().await?;
    // Check the token_accounts field in the result
    let fallback = vec![];
    let token_accounts = json["result"]["token_accounts"]
        .as_array()
        .unwrap_or(&fallback);
    
    if token_accounts.is_empty() {
        let create_ata_ix = create_associated_token_account(
            &user_keypair.pubkey(),
            &user_keypair.pubkey(),
            mint_address,
            &spl_token::id(),
        );
        Ok(Some(create_ata_ix))
    } else {
        Ok(None)
    }
}

pub async fn create_pump_fun_swap(
    user_keypair: &Keypair,
    dev_user_keypair: &str,
    mint_address: &Pubkey,
    pool_address: &Pubkey,
    block_hash: Hash,
    ata_instruction: Option<Instruction>,
    client: &Client,
    is_buy: bool,
) -> Result<Vec<TransactionMessage>, Box<dyn std::error::Error + Send + Sync>> {

    let mut payload = None;
    if is_buy {
        payload = Some(json!({
            "userAddress": user_keypair.pubkey().to_string(),
            "bondingCurveAddress": pool_address.to_string(),
            "tokenAddress": mint_address.to_string(),
            "tokenAmount": 400000,
            "solThreshold": ********,
            "isBuy": true,
            "creator": dev_user_keypair, // Use the actual token creator (dev)
            "slippage": 0.2,
            "computePrice": 1_000_000,  // optional
            "tip": 1_000_000    
        }));
    } else {
        payload = Some(json!({
            "userAddress": user_keypair.pubkey().to_string(),
            "bondingCurveAddress": pool_address.to_string(),
            "tokenAddress": mint_address.to_string(),
            "tokenAmount": 400000,
            "solThreshold": 0.0001,
            "isBuy": false,
            "creator": dev_user_keypair, // Use the actual token creator (dev)
            "slippage": 0.2,
            "computePrice": 1_000_000,  // optional
            "tip": 1_000_000    
        }));
    }
    
    let res = client
        .post("https://pump-ny.solana.dex.blxrbdn.com/api/v2/pumpfun/swap")
        .header("Authorization", "YWYzMjAzZWYtZTdhMC00Y2U5LWI0ODUtNzI2OGNjYWZlMzNiOmRlNmRmNDg2NWExZjQwMmEyYWNjMzQzNjM3MDY0ZmQ5")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await?;

    let res_json: serde_json::Value = res.json().await?;
    println!("res_json: {:?}", res_json);
    let tx_base64 = res_json["transaction"]["content"]
        .as_str()
        .ok_or("Invalid response: missing base64 transaction")?;

    let tx_bytes = decode(tx_base64)?;
    let mut tx: VersionedTransaction = bincode::deserialize(&tx_bytes)?;

    if let Some(ata_ix) = ata_instruction {
        // Only inject if the swap tx doesn't already include one
        if !tx.message.static_account_keys().iter().any(|k| *k == get_associated_token_address(&user_keypair.pubkey(), mint_address)) {
            if let VersionedMessage::Legacy(ref mut msg) = tx.message {
                msg.instructions.insert(0, msg.compile_instruction(&ata_ix));
            }
        }
    }

    let signed_tx = VersionedTransaction::try_new(
        tx.message.clone(),
        &[user_keypair]
    ).map_err(|e| format!("Failed to sign transaction: {}", e))?;

    let signed_transaction_bytes = bincode::serialize(&signed_tx)
        .map_err(|e| format!("Failed to serialize signed transaction: {}", e))?;
    let encoded_swap_tx = general_purpose::STANDARD.encode(signed_transaction_bytes);

    Ok(vec![TransactionMessage {
        content: encoded_swap_tx,
        is_cleanup: false,
    }])
}

pub async fn create_raydium_swap(
    user_keypair: &Keypair,
    dev_user_keypair: &str,
    mint_address: &Pubkey,
    pool_address: &Pubkey,
    block_hash: Hash,
    ata_instruction: Option<Instruction>,
    client: &Client,
    is_buy: bool,
) -> Result<Vec<TransactionMessage>, Box<dyn std::error::Error + Send + Sync>> {

    let mut payload = None;
    if is_buy {
        payload = Some(json!({
            "ownerAddress": user_keypair.pubkey().to_string(),
            "inToken": "So11111111111111111111111111111111111111112",
            "outToken": mint_address.to_string(),
            "inAmount": 0.1,
            "slippage": 30,
            "computePrice": 1_000_000,  // optional
            "tip": 1_000_000    
        }));
    } else {
        payload = Some(json!({
            "ownerAddress": user_keypair.pubkey().to_string(),
            "inToken": mint_address.to_string(),
            "outToken": "So11111111111111111111111111111111111111112",
            "inAmount": 0.1,
            "slippage": 30,
            "computePrice": 1_000_000,  // optional
            "tip": 1_000_000    
        }));
    }
    
    let res = client
        .post("https://ny.solana.dex.blxrbdn.com/api/v2/jupiter/swap")
        .header("Authorization", "YWYzMjAzZWYtZTdhMC00Y2U5LWI0ODUtNzI2OGNjYWZlMzNiOmRlNmRmNDg2NWExZjQwMmEyYWNjMzQzNjM3MDY0ZmQ5")
        .header("Content-Type", "application/json")
        .json(&payload)
        .send()
        .await?;

    let res_json: serde_json::Value = res.json().await?;
    println!("res_json: {:?}", res_json);
    let tx_base64 = res_json["transactions"][0]["content"]
        .as_str()
        .ok_or("Invalid response: missing base64 transaction")?;

    let tx_bytes = decode(tx_base64)?;
    let mut tx: VersionedTransaction = bincode::deserialize(&tx_bytes)?;

    if let Some(ata_ix) = ata_instruction {
        // Only inject if the swap tx doesn't already include one
        if !tx.message.static_account_keys().iter().any(|k| *k == get_associated_token_address(&user_keypair.pubkey(), mint_address)) {
            if let VersionedMessage::Legacy(ref mut msg) = tx.message {
                msg.instructions.insert(0, msg.compile_instruction(&ata_ix));
            }
        }
    }

    let signed_tx = VersionedTransaction::try_new(
        tx.message.clone(),
        &[user_keypair]
    ).map_err(|e| format!("Failed to sign transaction: {}", e))?;

    let signed_transaction_bytes = bincode::serialize(&signed_tx)
        .map_err(|e| format!("Failed to serialize signed transaction: {}", e))?;
    let encoded_swap_tx = general_purpose::STANDARD.encode(signed_transaction_bytes);

    Ok(vec![TransactionMessage {
        content: encoded_swap_tx,
        is_cleanup: false,
    }])
}

/*
pub async fn send_slack_notification(message: &str) -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();
    let webhook_url = env::var("SLACK_WEBHOOK_URL").unwrap_or_default();

    let client = reqwest::Client::new();
    let payload = serde_json::json!({
        "text": message,
        "username": "Solana Trader Bot",
        "icon_emoji": ":rocket:"
    });

    let res = client
        .post(webhook_url)
        .json(&payload)
        .send()
        .await?;

    if !res.status().is_success() {
        eprintln!("Failed to send Slack notification: {:?}", res.text().await?);
    }
    
    Ok(())
}*/
