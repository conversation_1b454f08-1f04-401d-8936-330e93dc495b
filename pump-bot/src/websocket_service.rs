use std::sync::{<PERSON>, Mutex, mpsc};
use std::thread;
use websocket::client::ClientBuilder;
use websocket::{Message, OwnedMessage};
use websocket::sync::Client;
use websocket::native_tls::TlsStream;
use std::net::TcpStream;
use serde::Serialize;
use serde_json;
use tokio::sync::broadcast;

pub struct WebSocketService {
    tx: mpsc::Sender<OwnedMessage>,
    rx_from_ws: broadcast::Receiver<String>,
    send_handle: Option<thread::Join<PERSON>andle<()>>,
    receive_handle: Option<thread::Join<PERSON><PERSON>le<()>>,
}

impl WebSocketService {
    pub fn new(connection_url: &str) -> Result<Self, Box<dyn std::error::Error>> {
        let client = ClientBuilder::new(connection_url)
            .unwrap()
            .add_protocol("rust-websocket")
            .connect_secure(None)
            .unwrap();

        let client = Arc::new(Mutex::new(client));
        let (tx, rx) = mpsc::channel();
        let (msg_tx, msg_rx) = broadcast::channel(1000);
        let tx_for_recv = tx.clone();

        // Sending thread
        let client_send: Arc<Mutex<Client<TlsStream<TcpStream>>>> = Arc::clone(&client);
        let send_handle = thread::spawn(move || {
            while let Ok(message) = rx.recv() {
                let mut client = client_send.lock().unwrap();
                if let Err(e) = client.send_message(&message) {
                    eprintln!("Send Loop: {:?}", e);
                    break;
                }
                if let OwnedMessage::Close(_) = message {
                    break;
                }
            }
        });

        // Receiving thread
        let client_recv: Arc<Mutex<Client<TlsStream<TcpStream>>>> = Arc::clone(&client);
        let receive_handle = thread::spawn(move || {
            loop {
                let message = {
                    let mut client = client_recv.lock().unwrap();
                    client.recv_message()
                };
                match message {
                    Ok(OwnedMessage::Text(txt)) => {
                        let _ = msg_tx.send(txt).unwrap();
                    }
                    Ok(OwnedMessage::Close(_)) => {
                        break;
                    }
                    Ok(OwnedMessage::Ping(data)) => {
                        let _ = tx_for_recv.send(OwnedMessage::Pong(data));
                    }
                    Ok(msg) => {
                        // Print or handle other message types if needed
                        //println!("Receive Loop: {:?}", msg);
                    }
                    Err(e) => {
                        eprintln!("Receive Loop: {:?}", e);
                        break;
                    }
                }
            }
        });

        Ok(WebSocketService {
            tx,
            rx_from_ws: msg_rx,
            send_handle: Some(send_handle),
            receive_handle: Some(receive_handle),
        })
    }

    pub fn join_room(&self, room: &str) {
        #[derive(Serialize)]
        struct JoinMessage<'a> {
            #[serde(rename = "type")]
            msg_type: &'a str,
            room: &'a str,
        }
        let join_msg = JoinMessage { msg_type: "join", room };
        let join_json = serde_json::to_string(&join_msg).unwrap_or_default();
        let _ = self.tx.send(OwnedMessage::Text(join_json));
    }

    pub fn leave_room(&self, room: &str) -> Result<(), Box<dyn std::error::Error>> {
        #[derive(Serialize)]
        struct LeaveMessage<'a> {
            #[serde(rename = "type")]
            msg_type: &'a str,
            room: &'a str,
        }
        let leave_msg = LeaveMessage { msg_type: "leave", room };
        let leave_json = serde_json::to_string(&leave_msg)?;
        self.tx.send(OwnedMessage::Text(leave_json))?;
        Ok(())
    }

    pub fn send_message(&self, message: &str) -> Result<(), Box<dyn std::error::Error>> {
        self.tx.send(OwnedMessage::Text(message.to_string()))?;
        Ok(())
    }

    pub fn send_ping(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.tx.send(OwnedMessage::Ping(b"PING".to_vec()))?;
        Ok(())
    }

    pub fn close(&self) -> Result<(), Box<dyn std::error::Error>> {
        self.tx.send(OwnedMessage::Close(None))?;
        Ok(())
    }

    pub fn get_message_receiver(&self) -> &broadcast::Receiver<String> {
        &self.rx_from_ws
    }

    pub fn wait_for_exit(&mut self) {
        if let Some(handle) = self.send_handle.take() {
            let _ = handle.join();
        }
        if let Some(handle) = self.receive_handle.take() {
            let _ = handle.join();
        }
    }
}

impl Drop for WebSocketService {
    fn drop(&mut self) {
        let _ = self.close();
        self.wait_for_exit();
    }
} 