use reqwest::Client;
use reqwest::header::{HeaderMap, HeaderValue, COOKIE, USER_AGENT};
use serde_json::Value;
use serde::{Deserialize};

#[derive(Deserialize, Debug)]
struct AxiomResponse {
    twitter: Option<String>,
}

#[derive(Debug, Deserialize)]
struct Risk {
    level: Option<String>,
}

#[derive(Debug, Deserialize)]
struct RugCheckResponse {
    risks: Option<Vec<Risk>>,
}

#[derive(Debug, Deserialize)]
struct TwitterUserInfo {
    followers: u32,
    #[serde(rename = "userName")]
    username: String,
    name: String,
    #[serde(rename = "isBlueVerified")]
    is_blue_verified: bool,
}

#[derive(Deserialize)]
pub struct MarketCapResponse {
    pub price: f64,
    pub marketCap: f64,
}

#[derive(Debug, Deserialize)]
pub struct MultiTokenMarketCapData {
    pub price: f64,
    pub priceQuote: f64,
    pub liquidity: f64,
    pub marketCap: f64,
    pub lastUpdated: u64,
}

#[derive(Debug, Deserialize)]
pub struct MultiTokenMarketCapResponse {
    #[serde(flatten)]
    pub tokens: std::collections::HashMap<String, MultiTokenMarketCapData>,
}

#[derive(Debug, Deserialize)]
struct JupiterPriceData {
    id: String,
    #[serde(rename = "type")]
    price_type: String,
    price: String,
}

#[derive(Debug, Deserialize)]
struct JupiterPriceResponse {
    data: std::collections::HashMap<String, JupiterPriceData>,
    timeTaken: f64,
}

#[derive(Debug, Deserialize, Clone)]
pub struct TradeInfo {
    pub tx: String,
    pub priceUsd: f64,
    #[serde(rename = "type")]
    pub trade_type: String,
    pub amount: f64,    
    pub wallet: String,
    pub time: u64,
}

#[derive(Debug, Deserialize)]
struct TradeResponse {
    trades: Vec<TradeInfo>,
}

pub async fn get_token_info(
    token_address: &str,
) -> Result<Value, Box<dyn std::error::Error + Send + Sync>> {

    dotenv::dotenv().ok();
    let SOL_TRACK_API_KEY = std::env::var("SOL_TRACK_API_KEY").unwrap_or_default();
    let url = format!("https://data.solanatracker.io/tokens/{}", token_address);

    let mut headers = HeaderMap::new();
    let api_key = SOL_TRACK_API_KEY;
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);
    headers.insert(USER_AGENT, HeaderValue::from_static("rust-client"));

    let client = reqwest::Client::new();
    let resp = client.get(&url).headers(headers).send().await?;

    if resp.status().is_success() {
        let json: Value = resp.json().await?;
        Ok(json)
    } else {
        Err(format!("HTTP error: {}", resp.status()).into())
    }
}

pub async fn check_axiom_twitter_field(pair_address: &str) -> Result<bool, String> {
    let url = format!(
        "https://api2.axiom.trade/token-info?pairAddress={}",
        pair_address
    );

    let cookie_str = "auth-refresh-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjU4ZDY1MmYyLWUyYzQtNDFkMS05ZTFkLTAwYmE5ZmU3N2U4ZiIsImlhdCI6MTc1MjE3MjgyOH0.MZuVfDBg_JBtatgq6qJnSWEhGYvgjgr6H0aiJ-ZJoHo; auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGVkVXNlcklkIjoiYzRiZmNhOTEtYjhhMy00ZWUzLTgzMDktYWMxYTM2MzIyMzM0IiwiaWF0IjoxNzUzMzUxMDIxLCJleHAiOjE3NTMzNTE5ODF9.zkWBKJP4i3l1cM22ma8dJ6Lf0tp5nRZlTydfmqX5n5w";

    let mut headers = HeaderMap::new();
    headers.insert(COOKIE, HeaderValue::from_str(cookie_str).unwrap());

    let client = reqwest::Client::new();
    let resp = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = resp.status();
    if !status.is_success() {
        return Err(format!("API returned error status: {}", status));
    }

    let body: AxiomResponse = resp
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    Ok(body.twitter.is_some())
}

pub async fn get_rugcheck_risk_level(mint: &str) -> Result<u8, String> {
    let url = format!("https://api.rugcheck.xyz/v1/tokens/{}/report", mint);
    let client = Client::new();

    let response = client
        .get(&url)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = response.status();
    let text = response.text().await.map_err(|e| format!("Read failed: {}", e))?;

    if !status.is_success() {
        return Ok(4) // Invalid mint or rugcheck API error
    }

    // Try parsing actual rugcheck response
    let data: RugCheckResponse = serde_json::from_str(&text)
        .map_err(|e| format!("Failed to parse JSON: {} - raw: {}", e, text))?;

    if let Some(risks) = data.risks {
        let mut has_warn = false;
        for risk in risks {
            if let Some(level) = &risk.level {
                if level == "danger" {
                    return Ok(2);
                } else if level == "warn" {
                    has_warn = true;
                }
            }
        }
        if has_warn {
            return Ok(1);
        }
    }

    Ok(0)
}

pub async fn get_twitter_followers(twitter_handle: &str) -> Result<u32, Box<dyn std::error::Error + Send + Sync>> {
    let url = format!(
        "https://api8.axiom.trade/twitter-user-info?twitterHandle={}",
        twitter_handle
    );

    let cookie_str = "auth-refresh-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWZyZXNoVG9rZW5JZCI6IjU5NmFlZDNjLTdjMmEtNDJiMi04MzljLWQ4YzNjMzFlYzBhNiIsImlhdCI6MTczOTYwMzMwM30.yqQQPuOQ5_kujo3c_iqFsIybcQ_BnpB4RxYaRVGEH3c; ph_phc_7bPgugSDujyCK9a1776BMM9UMGTNl2bUxGyg2UJuykr_posthog=%7B%22distinct_id%22%3A%2201950234-160a-714f-b052-50c47b6b1497%22%2C%22%24sesid%22%3A%5B1739768331650%2C%2201951243-66c8-7b6e-bec0-a7c01acbada6%22%2C1739768161992%5D%2C%22%24epp%22%3Atrue%2C%22%24initial_person_info%22%3A%7B%22r%22%3A%22%24direct%22%2C%22u%22%3A%22https%3A%2F%2Faxiom.trade%2F%40smcmatt%22%7D%7D; auth-access-token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhdXRoZW50aWNhdGVkVXNlcklkIjoiYzRiZmNhOTEtYjhhMy00ZWUzLTgzMDktYWMxYTM2MzIyMzM0IiwiaWF0IjoxNzQ5NDQ3OTcyLCJleHAiOjE3NDk0NDg5MzJ9.jmLecNB5mt_ATpLjoTjZ1QDRO8b4-BTnnyX2dTKPng8";

    let mut headers = HeaderMap::new();
    headers.insert(COOKIE, HeaderValue::from_str(cookie_str).unwrap());

    let client = reqwest::Client::new();
    let resp = client
        .get(&url)
        .headers(headers)
        .send()
        .await
        .map_err(|e| format!("Request failed: {}", e))?;

    let status = resp.status();
    if !status.is_success() {
        return Err(format!("API returned error status: {}", status).into());
    }

    let user_info: TwitterUserInfo = resp
        .json()
        .await
        .map_err(|e| format!("Failed to parse JSON: {}", e))?;

    Ok(user_info.followers)
}
/*
pub async fn send_slack_notification(message: &str) -> Result<(), Box<dyn std::error::Error>> {
    dotenv::dotenv().ok();
    let webhook_url = env::var("SLACK_WEBHOOK_URL").unwrap_or_default();

    let client = reqwest::Client::new();
    let payload = serde_json::json!({
        "text": message,
        "username": "Solana Trader Bot",
        "icon_emoji": ":rocket:"
    });

    let res = client
        .post(webhook_url)
        .json(&payload)
        .send()
        .await?;

    if !res.status().is_success() {
        eprintln!("Failed to send Slack notification: {:?}", res.text().await?);
    }
    
    Ok(())
}
*/
pub async fn get_price(mint_key: &str) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
    dotenv::dotenv().ok();
    let SOL_TRACK_API_KEY = std::env::var("SOL_TRACK_API_KEY").unwrap_or_default();
    
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));
    
    let api_key = SOL_TRACK_API_KEY;
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);

    let url = format!("https://data.solanatracker.io/price?token={}", mint_key);
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()).into());
    }

    let market_cap_data: MarketCapResponse = response.json().await?;
    Ok(market_cap_data.price)
}

pub async fn get_price_and_market_cap(mint_key: &str) -> Result<(f64, f64), Box<dyn std::error::Error + Send + Sync>> {
    dotenv::dotenv().ok();
    let SOL_TRACK_API_KEY = std::env::var("SOL_TRACK_API_KEY").unwrap_or_default();
    
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));
    
    let api_key = SOL_TRACK_API_KEY;
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);

    let url = format!("https://data.solanatracker.io/price?token={}", mint_key);
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()).into());
    }

    let market_cap_data: MarketCapResponse = response.json().await?;
    Ok((market_cap_data.price, market_cap_data.marketCap))
}

pub async fn get_price_multiple_tokens(mint_keys: &[String]) -> Result<std::collections::HashMap<String, (f64, u64)>, Box<dyn std::error::Error + Send + Sync>> {
    if mint_keys.is_empty() {
        return Ok(std::collections::HashMap::new());
    }

    dotenv::dotenv().ok();
    let SOL_TRACK_API_KEY = std::env::var("SOL_TRACK_API_KEY").unwrap_or_default();
    
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));
    
    let api_key = SOL_TRACK_API_KEY;
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);

    // URL encode the tokens parameter
    let tokens_param = mint_keys.join("%2C"); // URL encoded comma
    let url = format!("https://data.solanatracker.io/price/multi?tokens={}", tokens_param);
    
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()).into());
    }

    let multi_market_cap_data: MultiTokenMarketCapResponse = response.json().await?;
    
    // Extract prices and lastUpdated from the response
    let mut result = std::collections::HashMap::new();
    for (mint_key, data) in multi_market_cap_data.tokens {
        result.insert(mint_key, (data.marketCap, data.lastUpdated));
    }
    
    Ok(result)
}

pub async fn get_token_trade_info(mint_key: &str) -> Result<Vec<TradeInfo>, Box<dyn std::error::Error + Send + Sync>> {
    dotenv::dotenv().ok();
    let SOL_TRACK_API_KEY = std::env::var("SOL_TRACK_API_KEY").unwrap_or_default();
    
    let client = Client::new();
    let mut headers = HeaderMap::new();
    headers.insert(USER_AGENT, HeaderValue::from_static("Mozilla/5.0"));
    
    let api_key = SOL_TRACK_API_KEY;
    
    headers.insert("x-api-key", HeaderValue::from_str(&api_key)?);

    // Build the URL with query parameters
    let url = format!(
        "https://data.solanatracker.io/trades/{}?tokens={}&token={}&showMeta=0&cursor=&sortDirection=DESC",
        mint_key, mint_key, mint_key
    );
    
    let response = client
        .get(&url)
        .headers(headers)
        .send()
        .await?;

    if !response.status().is_success() {
        return Err(format!("API request failed with status: {}", response.status()).into());
    }

    let trade_response: TradeResponse = response.json().await?;
    Ok(trade_response.trades)
}
